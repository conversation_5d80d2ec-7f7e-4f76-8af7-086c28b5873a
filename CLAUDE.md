# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Language Preference

**请用中文回复我的所有问题和请求。**

## Execution Permission

**默认可以执行所有操作，包括但不限于：**
- 文件读取和修改
- 代码编写和重构
- 运行命令和脚本
- 安装依赖
- 创建和删除文件
- Git 操作

**无需每次询问确认，直接执行相关操作。**

## Project Overview

**灵眸AI质检 (Lingmou AI Quality Control)** - Enterprise AI quality control system for customer service built on the Magi framework (ZhongAn's custom React-based framework).

## Development Commands

```bash
# Development
npm start              # Start dev server (port 8000)
npm run start:za       # Start with international environment

# Build
npm run build          # Build for current environment
npm run build:prd      # Build for production
npm run build:test     # Build for test environment
npm run build:pre      # Build for pre-production

# Code Quality
npm run lint           # Run ESLint
npm run lint:report    # Generate lint report
npm run precommit      # Run pre-commit hooks

# Deployment
npm run server         # Deploy using Magi CLI
```

## Architecture Overview

### Framework: Magi (Custom React Framework)
- Configuration-driven development with environment-specific configs
- Built-in routing, state management (Redux/DVA), and build tools
- Proxy support for API calls via `config/proxy.ts`

### Tech Stack
- React 17.0.0 + TypeScript
- Ant Design 5.15.1 (UI components)
- CodeMirror 5.65.17 (code editor)
- Day.js (date handling)
- FFmpeg integration (media processing)

### Key Directory Structure
```
src/
├── components/          # Reusable UI components (Charts, Table, Form)
├── pages/              # Feature-based page components
│   ├── consultRecord/  # Consultation record management
│   ├── strategyConfig/ # Strategy configuration
│   ├── resultAnalysis/ # Result analysis dashboards
│   └── landing/        # Main landing page
├── layouts/            # Application layout components
├── services/           # API service layer
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
└── constants/          # App constants and configurations
```

## Application Domain

AI Quality Control System for customer service with core features:
- **Consultation Records**: Audio/video call analysis and quality assessment
- **Strategy Configuration**: AI rule templates and quality control strategies  
- **Result Analysis**: Quality metrics dashboards and analytics
- **Real-time Monitoring**: Live quality control tracking
- **Batch Testing**: Bulk quality assessment tools
- **Red Line Management**: Critical violation tracking and alerts

## Configuration Management

### Environment Configs
- `config/config.ts` - Base configuration
- `config/config.dev.ts` - Development environment
- `config/config.prd.ts` - Production environment
- `config/proxy.ts` - API proxy settings

### Build Considerations
- Uses hash-based routing (`hashHistory: { type: 'hash' }`)
- Fast Refresh enabled for hot reloading
- Source maps optimized for development
- SASS deprecation warnings suppressed in build

## Media Processing Architecture

Specialized handling for audio/video content:
- FFmpeg integration for media conversion
- Custom audio players for AMR and SILK formats
- Real-time audio visualization components
- Media upload and processing pipelines

## Enterprise Integration

- SSO integration with ZhongAn authentication system
- Multi-environment deployment (dev/test/pre/prd)
- Docker containerization ready (`Dockerfile` + `server.js`)
- Nginx configuration for production deployment

## State Management Patterns

- Global state via Redux/DVA through Magi framework
- Component-level state with React hooks
- Custom hooks for authentication, data fetching, and media handling
- Service layer abstraction for API calls

## Development Notes

- Uses React Fast Refresh instead of traditional hot reload
- Component lazy loading with dynamic imports for performance
- Consistent use of Ant Design components for UI consistency
- TypeScript strict mode enabled
- CSS Modules for component styling isolation